<template>
    <div class="login-container">
        <!-- 登录表单 -->
        <div class="login-form">
            <div class="login-header">
                <h2>渔船管控系统</h2>
                <p>请登录您的账户</p>
            </div>
            
            <form @submit.prevent="handleLogin">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input 
                        type="text" 
                        id="username" 
                        v-model="loginForm.username" 
                        placeholder="请输入用户名"
                        required
                        :disabled="isLoading"
                    />
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input 
                        type="password" 
                        id="password" 
                        v-model="loginForm.password" 
                        placeholder="请输入密码"
                        required
                        :disabled="isLoading"
                    />
                </div>
                
                <button type="submit" class="login-btn" :disabled="isLoading">
                    <span v-if="isLoading">登录中...</span>
                    <span v-else>登录</span>
                </button>
            </form>
            
            <!-- 错误提示 -->
            <div v-if="errorMessage" class="error-message">
                {{ errorMessage }}
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';

export default {
    name: 'LoginPage',
    data() {
        return {
            // 登录表单数据
            loginForm: {
                username: '',
                password: ''
            },
            // 加载状态
            isLoading: false,
            // 错误信息
            errorMessage: ''
        };
    },
    mounted() {
        // 检查是否已经登录，如果已登录则跳转到首页
        if (this.isLoggedIn()) {
            this.$router.push('/');
        }
    },
    methods: {
        /**
         * 处理登录提交
         */
        async handleLogin() {
            // 清除之前的错误信息
            this.errorMessage = '';
            
            // 验证表单
            if (!this.loginForm.username.trim()) {
                this.errorMessage = '请输入用户名';
                return;
            }
            
            if (!this.loginForm.password.trim()) {
                this.errorMessage = '请输入密码';
                return;
            }
            
            // 设置加载状态
            this.isLoading = true;
            
            try {
                // 调用登录API
                await this.login();
            } catch (error) {
                console.error('登录失败:', error);
                this.errorMessage = error.message || '登录失败，请重试';
            } finally {
                this.isLoading = false;
            }
        },
        
        /**
         * 登录API调用
         */
        login() {
            return new Promise((resolve, reject) => {
                const data = {
                    username: this.loginForm.username.trim(),
                    password: this.loginForm.password.trim()
                };
                
                $.ajax({
                    url: global.IP + "/web/Login",
                    type: "POST",
                    data: JSON.stringify(data),
                    dataType: "json",
                    contentType: "application/json",
                    success: (response) => {
                        console.log('登录响应:', response);
                        
                        if (response.state === "登陆成功") {
                            // 保存用户信息到sessionStorage
                            sessionStorage.setItem("isLogin", "true");
                            sessionStorage.setItem("userId", response.id);
                            sessionStorage.setItem("username", response.username);
                            sessionStorage.setItem("userArea", response.userArea || ''); // 保存用户区域信息
                            sessionStorage.setItem("shipQueryAreas", response.shipQueryAreas || ''); // 保存用户可查询的船舶区域信息
                            sessionStorage.setItem("jurisdiction", response.level);
                            sessionStorage.setItem("systemPage", "0");

                            // 记录登录操作日志
                            this.recordLoginOperation(response.id);

                            // 跳转到首页
                            this.$router.push('/');
                            resolve(response);
                        } else {
                            // 登录失败
                            reject(new Error(response.state || '用户名或密码错误'));
                        }
                    },
                    error: (xhr, status, error) => {
                        console.error('登录请求失败:', error);
                        reject(new Error('网络错误，请检查网络连接'));
                    }
                });
            });
        },
        
        /**
         * 记录登录操作日志
         */
        recordLoginOperation(userId) {
            const now = new Date();
            const timeElement = {
                year: now.getFullYear(),
                month: String(now.getMonth() + 1).padStart(2, '0'),
                date: String(now.getDate()).padStart(2, '0'),
                hour: String(now.getHours()).padStart(2, '0'),
                minute: String(now.getMinutes()).padStart(2, '0'),
                second: String(now.getSeconds()).padStart(2, '0')
            };
            
            const time = `${timeElement.year}-${timeElement.month}-${timeElement.date} ${timeElement.hour}:${timeElement.minute}:${timeElement.second}`;
            
            const operationInfo = {
                userId: userId,
                type: 0,
                content: "登录成功",
                loadTime: time
            };
            
            $.ajax({
                url: global.IP + "/web/SetUserOperationInfo",
                type: "POST",
                data: JSON.stringify(operationInfo),
                dataType: "json",
                contentType: "application/json",
                success: (data) => {
                    console.log('操作日志记录成功');
                },
                error: (error) => {
                    console.error('操作日志记录失败:', error);
                }
            });
        },
        
        /**
         * 检查用户是否已登录
         */
        isLoggedIn() {
            return sessionStorage.getItem("isLogin") === "true";
        }
    }
};
</script>

<style scoped>
.login-container {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Arial', sans-serif;
}

.login-form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    margin: 20px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
}

.login-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    margin-top: 15px;
    padding: 12px;
    background-color: #fee;
    border: 1px solid #fcc;
    border-radius: 6px;
    color: #c33;
    font-size: 14px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-form {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-header h2 {
        font-size: 24px;
    }
}
</style>
